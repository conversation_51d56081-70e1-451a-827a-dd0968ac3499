#!/usr/bin/env python3
"""
Script to convert remaining logging calls to structured debug() calls in state_manager.py
"""

import re
import sys
from pathlib import Path

def convert_logging_calls(file_path):
    """Convert logging calls to structured debug() calls"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Track changes
    changes_made = 0
    
    # Pattern 1: Replace import logging and logger = logging.getLogger() patterns
    import_pattern = r'(\s+)import logging\s*\n\s+logger = logging\.getLogger\([^)]+\)'
    def replace_import(match):
        indent = match.group(1)
        return f'{indent}from debug_utils import debug'
    
    content, count = re.subn(import_pattern, replace_import, content)
    changes_made += count
    print(f"Replaced {count} import/logger patterns")
    
    # Pattern 2: Replace standalone logger.info/warning/error calls
    # This pattern handles simple logging calls
    simple_log_pattern = r'(\s+)logger\.(info|warning|error)\(([^)]+)\)'
    
    def replace_simple_log(match):
        indent = match.group(1)
        level = match.group(2)
        message = match.group(3)
        
        # Determine operation based on context and level
        if 'error' in level:
            operation = 'error_handling'
        elif 'warning' in level:
            operation = 'validation_warning'
        else:
            operation = 'state_update'
        
        # Clean up the message - remove f-string prefix if present
        message = message.strip()
        if message.startswith('f"') and message.endswith('"'):
            message = message[1:]  # Remove 'f' prefix
        elif message.startswith("f'") and message.endswith("'"):
            message = message[1:]  # Remove 'f' prefix
        
        return f'{indent}debug({message}, stage="state_management", operation="{operation}")'
    
    content, count = re.subn(simple_log_pattern, replace_simple_log, content)
    changes_made += count
    print(f"Replaced {count} simple logging calls")
    
    # Pattern 3: Handle multi-line logging calls (more complex)
    # Look for logger calls that span multiple lines or have complex formatting
    complex_log_pattern = r'(\s+)logger\.(info|warning|error)\(f?"([^"]*(?:\{[^}]*\}[^"]*)*)"(?:,\s*[^)]+)?\)'
    
    def replace_complex_log(match):
        indent = match.group(1)
        level = match.group(2)
        message = match.group(3)
        
        # Determine operation based on message content
        if 'State change:' in message:
            operation = 'state_change'
        elif 'CRITICAL' in message:
            operation = 'critical_operation'
        elif 'error' in level or 'Error' in message or 'Failed' in message:
            operation = 'error_handling'
        elif 'warning' in level or 'Warning' in message:
            operation = 'validation_warning'
        elif 'JSON' in message or 'storage' in message:
            operation = 'data_persistence'
        elif 'stage' in message.lower() or 'Stage' in message:
            operation = 'stage_management'
        elif 'script' in message.lower():
            operation = 'script_management'
        else:
            operation = 'general_operation'
        
        return f'{indent}debug(f"{message}", stage="state_management", operation="{operation}")'
    
    content, count = re.subn(complex_log_pattern, replace_complex_log, content)
    changes_made += count
    print(f"Replaced {count} complex logging calls")
    
    # Write the updated content back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Total changes made: {changes_made}")
    return changes_made

if __name__ == "__main__":
    file_path = "state_manager.py"
    if not Path(file_path).exists():
        print(f"Error: {file_path} not found")
        sys.exit(1)
    
    print(f"Converting logging calls in {file_path}...")
    changes = convert_logging_calls(file_path)
    
    if changes > 0:
        print(f"✅ Successfully converted {changes} logging calls")
        print("Please review the changes and test the file")
    else:
        print("No logging calls found to convert")
