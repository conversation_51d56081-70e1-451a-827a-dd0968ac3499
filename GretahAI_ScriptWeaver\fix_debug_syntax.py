#!/usr/bin/env python3
"""
Script to fix syntax errors in debug() calls caused by the automated conversion
"""

import re
import sys
from pathlib import Path

def fix_debug_syntax_errors(file_path):
    """Fix syntax errors in debug() calls"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Track changes
    changes_made = 0
    
    # Pattern 1: Fix debug calls with malformed f-strings
    # Example: debug(f"message {var, stage="...", operation="..."}")
    # Should be: debug(f"message {var}", stage="...", operation="...")
    malformed_fstring_pattern = r'debug\(f?"([^"]*\{[^}]*), stage="([^"]*)", operation="([^"]*)"\}([^"]*)"(?:, stage="[^"]*", operation="[^"]*")?\)'
    
    def fix_malformed_fstring(match):
        message_start = match.group(1)
        stage = match.group(2)
        operation = match.group(3)
        message_end = match.group(4)
        
        # Reconstruct the proper f-string
        full_message = f'f"{message_start}{{{message_end}}}"'
        return f'debug({full_message}, stage="{stage}", operation="{operation}")'
    
    content, count = re.subn(malformed_fstring_pattern, fix_malformed_fstring, content)
    changes_made += count
    print(f"Fixed {count} malformed f-string debug calls")
    
    # Pattern 2: Fix debug calls with parameters inside f-string braces
    # Example: debug(f"message {var, stage="...", operation="..."}")
    # Should be: debug(f"message {var}", stage="...", operation="...")
    inside_braces_pattern = r'debug\(f?"([^"]*\{[^,}]*), stage="([^"]*)", operation="([^"]*)"([^}]*\}[^"]*)"(?:, stage="[^"]*", operation="[^"]*")?\)'
    
    def fix_inside_braces(match):
        message_start = match.group(1)
        stage = match.group(2) 
        operation = match.group(3)
        message_end = match.group(4)
        
        # Reconstruct the proper f-string
        full_message = f'f"{message_start}{message_end}"'
        return f'debug({full_message}, stage="{stage}", operation="{operation}")'
    
    content, count = re.subn(inside_braces_pattern, fix_inside_braces, content)
    changes_made += count
    print(f"Fixed {count} debug calls with parameters inside braces")
    
    # Pattern 3: Fix debug calls with missing quotes around f-strings
    # Example: debug(f"message", stage="...", operation="...")
    # Should be: debug(f"message", stage="...", operation="...")
    missing_quotes_pattern = r'debug\(([^"]*\{[^}]*\}[^"]*), stage="([^"]*)", operation="([^"]*)"\)'
    
    def fix_missing_quotes(match):
        message = match.group(1).strip()
        stage = match.group(2)
        operation = match.group(3)
        
        # Add quotes if missing
        if not (message.startswith('"') or message.startswith("'") or message.startswith('f"') or message.startswith("f'")):
            message = f'f"{message}"'
        
        return f'debug({message}, stage="{stage}", operation="{operation}")'
    
    content, count = re.subn(missing_quotes_pattern, fix_missing_quotes, content)
    changes_made += count
    print(f"Fixed {count} debug calls with missing quotes")
    
    # Pattern 4: Fix debug calls with extra parameters
    # Example: debug("message", stage="...", operation="...", stage="...", operation="...")
    # Should be: debug("message", stage="...", operation="...")
    duplicate_params_pattern = r'debug\(([^,]+), stage="([^"]*)", operation="([^"]*)"(?:, stage="[^"]*", operation="[^"]*")+\)'
    
    def fix_duplicate_params(match):
        message = match.group(1)
        stage = match.group(2)
        operation = match.group(3)
        
        return f'debug({message}, stage="{stage}", operation="{operation}")'
    
    content, count = re.subn(duplicate_params_pattern, fix_duplicate_params, content)
    changes_made += count
    print(f"Fixed {count} debug calls with duplicate parameters")
    
    # Pattern 5: Fix specific malformed calls we can identify
    specific_fixes = [
        # Fix calls with malformed parentheses
        (r'debug\(f"([^"]*\{[^}]*\}[^"]*)", stage="([^"]*)", operation="([^"]*)"\}([^"]*)"', 
         r'debug(f"\1\4", stage="\2", operation="\3")'),
        
        # Fix calls with missing closing parentheses
        (r'debug\("([^"]*)", stage="([^"]*)", operation="([^"]*)"(?!\))', 
         r'debug("\1", stage="\2", operation="\3")'),
    ]
    
    for pattern, replacement in specific_fixes:
        content, count = re.subn(pattern, replacement, content)
        changes_made += count
        if count > 0:
            print(f"Applied specific fix: {count} replacements")
    
    # Write the updated content back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Total syntax fixes made: {changes_made}")
    return changes_made

if __name__ == "__main__":
    file_path = "state_manager.py"
    if not Path(file_path).exists():
        print(f"Error: {file_path} not found")
        sys.exit(1)
    
    print(f"Fixing debug syntax errors in {file_path}...")
    changes = fix_debug_syntax_errors(file_path)
    
    if changes > 0:
        print(f"✅ Successfully fixed {changes} syntax errors")
        print("Please test the file compilation")
    else:
        print("No syntax errors found to fix")
